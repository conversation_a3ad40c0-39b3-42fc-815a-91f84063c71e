{{! 搜索框 - 仅当用户ID有效时显示 }}
{{#if SearchForm.CanSearch}}
{{#unless Message.IsError}}
<div class="bg-white p-4 border-b border-gray-100 sticky top-0 z-10">
    <div class="relative flex items-center">
        <form action="{{SearchForm.SearchUrl}}" method="get" class="w-full">
            <input type="hidden" name="action" value="class">
            <input type="hidden" name="siteid" value="{{SiteInfo.SiteId}}">
            <input type="hidden" name="classid" value="{{SiteInfo.ClassId}}">
            <input type="hidden" name="touserid" value="{{Permissions.TargetUserId}}">
            <input type="hidden" name="ot" value="{{Sort.CurrentSort}}">
            
            <div class="relative">
                <!-- 单一容器，搜索框 -->
                <div class="relative">
                    <input type="text" 
                           name="searchKey" 
                           value="{{SearchForm.SearchKey}}" 
                           class="w-full pl-4 pr-12 py-3 border border-gray-300 rounded-lg text-sm bg-white transition-all duration-200 focus:outline-none focus:border-teal-500 focus:shadow-lg focus:shadow-teal-500/10"
                           placeholder="多个关键词用空格隔开"
                           minlength="1" 
                           maxlength="30">
                    
                    <!-- 左侧筛选按钮 -->
                    {{#if Sort.ShowSort}}
                    <div class="absolute left-0 top-0 bottom-0 flex items-center justify-center pl-3 pr-2">
                        <button type="button" 
                                class="bg-transparent border-none rounded-full w-8 h-8 flex items-center justify-center text-gray-400 hover:bg-teal-50 hover:text-teal-500 transition-all duration-200 active:scale-90" 
                                onclick="toggleSortFilterDropdown()">
                            <i data-lucide="filter" class="w-5 h-5"></i>
                        </button>
                    </div>
                    <style>
                        /* 动态添加样式，确保输入文字与图标不重叠 */
                        input[name="searchKey"] {
                            padding-left: 3rem !important;
                        }
                    </style>
                    {{/if}}
                    
                    <!-- 右侧搜索按钮 -->
                    <div class="absolute right-1 top-1/2 transform -translate-y-1/2 flex items-center">
                    <button type="submit" class="bg-transparent border-none p-2 rounded cursor-pointer flex items-center justify-center text-gray-400 hover:bg-teal-50 hover:text-teal-500 transition-all duration-200 active:scale-90">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{{/unless}}

{{! 搜索框旁边的筛选下拉菜单 }}
{{#if Sort.ShowSort}}
<div id="sortFilterDropdown" class="dropdown-menu" style="transition: none !important;">
    <div class="dropdown-item {{#if Sort.IsNewest}}active{{/if}}" onclick="window.location.href='{{Sort.NewestUrl}}'">
        <i data-lucide="clock" class="w-4 h-4 mr-2"></i>按最新回复
    </div>
    <div class="dropdown-item {{#if Sort.IsOldest}}active{{/if}}" onclick="window.location.href='{{Sort.OldestUrl}}'">
        <i data-lucide="history" class="w-4 h-4 mr-2"></i>按最早回复
    </div>
</div>
{{/if}}
{{/if}}

{{! 选项下拉菜单 }}
<div id="optionsDropdown" class="dropdown-menu" style="transition: none !important;">
    {{#if AdminActions.ShowAdminActions}}
    <div class="dropdown-item text-red-500" onclick="confirmClearReplies('{{AdminActions.ClearRepliesUrl}}')">
        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>清空用户回复
    </div>
    {{/if}}
</div>

{{! 保留原有排序下拉菜单作为备选 }}
{{#if Sort.ShowSort}}
<div id="sortDropdown" class="dropdown-menu" style="display: none;">
    <div class="dropdown-item {{#if Sort.IsNewest}}active{{/if}}" onclick="window.location.href='{{Sort.NewestUrl}}'">
        按最新回复
    </div>
    <div class="dropdown-item {{#if Sort.IsOldest}}active{{/if}}" onclick="window.location.href='{{Sort.OldestUrl}}'">
        按最早回复
    </div>
</div>
{{/if}}

{{! 消息提示 }}
{{#if Message.HasMessage}}
<div class="px-4 py-3">
    <div class="p-4 rounded-lg {{#if Message.IsError}}bg-red-50 text-red-700 border border-red-200{{else}}bg-blue-50 text-blue-700 border border-blue-200{{/if}}">
        {{Message.Content}}
    </div>
</div>
{{/if}}

{{! 管理员操作区域已移至右上角三点菜单 }}

{{! 回复列表 }}
{{#if ReplyList}}
{{#each ReplyList}}
<div class="py-3 {{#if @first}}mt-0.5{{else}}border-t border-gray-100{{/if}} bg-white">
    <div class="flex justify-between items-center mb-2">
        <a href="{{UserInfoUrl}}" class="text-teal-500 font-medium hover:underline">{{Nickname}} ({{UserId}})</a>
        <div class="text-gray-400 text-sm">#{{Index}}</div>
    </div>
    <div class="mb-2 text-gray-900">{{{Content}}}</div>
    <div class="flex justify-between items-center">
        <div class="text-gray-400 text-xs flex items-center">
            <i data-lucide="clock" style="width: .65rem; height: .65rem; margin-right: .15rem;"></i>
            {{FormattedDate}}
        </div>
        <div class="text-gray-400">
            <a href="{{ViewUrl}}" class="hover:text-teal-500 transition-colors duration-200">
                <i data-lucide="eye" class="w-4 h-4"></i>
            </a>
        </div>
    </div>
</div>
{{/each}}
{{else}}
<div class="p-8 text-center">
    <i data-lucide="message-circle" class="w-16 h-16 mx-auto text-gray-300 mb-4"></i>
    <p class="text-gray-500">
        {{#if SearchForm.HasSearchResult}}
            没有找到匹配的回复记录
        {{else}}
            暂无回复记录
        {{/if}}
    </p>
    {{#if SearchForm.HasSearchResult}}
    <div class="mt-4">
        <a href="{{SearchForm.SearchUrl}}?action=class&siteid={{SiteInfo.SiteId}}&classid={{SiteInfo.ClassId}}&touserid={{Permissions.TargetUserId}}&ot={{Sort.CurrentSort}}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
            <i data-lucide="list" class="w-4 h-4 mr-2"></i>
            查看全部回复
        </a>
    </div>
    {{/if}}
</div>
{{/if}}

{{! 分页导航 }}
{{#if Pagination.ShowPagination}}
<div class="sticky bottom-0 bg-white border-t border-gray-100 p-4 mt-4">
    {{! 当总页数大于1时显示翻页按钮 }}
    {{#unless Pagination.IsFirstPage}}
    {{#unless Pagination.IsLastPage}}
    <div class="flex justify-center items-center gap-3" id="pagination-container">
        <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5" id="prevPageBtn">
            <i data-lucide="chevron-left" class="w-5 h-5"></i>
        </button>
        
        <!-- 这里会通过JavaScript生成页码按钮 -->
        
        <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5" id="nextPageBtn">
            <i data-lucide="chevron-right" class="w-5 h-5"></i>
        </button>
    </div>
    {{else}}
    {{! 在最后一页，只显示上一页按钮 }}
    <div class="flex justify-center items-center gap-3" id="pagination-container">
        <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5" id="prevPageBtn">
            <i data-lucide="chevron-left" class="w-5 h-5"></i>
        </button>
    </div>
    {{/unless}}
    {{else}}
    {{! 在第一页，且不是唯一页，只显示下一页按钮 }}
    {{#unless Pagination.IsLastPage}}
    <div class="flex justify-center items-center gap-3" id="pagination-container">
        <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5" id="nextPageBtn">
            <i data-lucide="chevron-right" class="w-5 h-5"></i>
        </button>
    </div>
    {{/unless}}
    {{/unless}}
    
    {{! 始终显示页码信息 }}
    <div class="text-center text-gray-500 text-sm {{#unless Pagination.IsFirstPage}}{{#unless Pagination.IsLastPage}}mt-2{{/unless}}{{/unless}}">
        第 {{Pagination.CurrentPage}}/{{Pagination.TotalPages}} 页，共 {{Pagination.TotalItems}} 条
    </div>
</div>
{{/if}}

{{! 不再需要隐藏原始分页的容器 }}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    lucide.createIcons();
    
    // 处理分页按钮点击事件
    initializePaginationButtons();
    
    window.addEventListener('scroll', function(event) {
        // 滚动时重新定位下拉菜单
        const dropdown = document.getElementById('sortDropdown');
        if (dropdown && dropdown.classList.contains('show')) {
            positionDropdown(dropdown);
        }
        const optionsDropdown = document.getElementById('optionsDropdown');
        if (optionsDropdown && optionsDropdown.classList.contains('show')) {
            positionDropdown(optionsDropdown);
        }
        const sortFilterDropdown = document.getElementById('sortFilterDropdown');
        if (sortFilterDropdown && sortFilterDropdown.classList.contains('show')) {
            positionSortFilterDropdown(sortFilterDropdown);
        }
    });
});

// 切换选项下拉菜单显示状态
function toggleOptionsDropdown() {
    const dropdown = document.getElementById('optionsDropdown');
    if (dropdown) {
        moveDropdownToBody(dropdown);
        dropdown.classList.toggle('show');
        if (dropdown.classList.contains('show')) {
            positionDropdown(dropdown);
        }
    }
}

// 切换搜索框旁边的筛选下拉菜单
function toggleSortFilterDropdown() {
    const dropdown = document.getElementById('sortFilterDropdown');
    if (dropdown) {
        moveDropdownToBody(dropdown);
        dropdown.classList.toggle('show');
        if (dropdown.classList.contains('show')) {
            positionSortFilterDropdown(dropdown);
        }
    }
}

// 切换排序下拉菜单显示状态（兼容旧代码）
function toggleSortDropdown() {
    const dropdown = document.getElementById('sortDropdown');
    if (dropdown) {
        moveDropdownToBody(dropdown);
        dropdown.classList.toggle('show');
        if (dropdown.classList.contains('show')) {
            positionDropdown(dropdown);
        }
    }
}

// 将下拉菜单移动到body末尾
function moveDropdownToBody(dropdown) {
    if (dropdown && dropdown.parentNode !== document.body) {
        document.body.appendChild(dropdown);
    }
}

// 下拉菜单定位
function positionDropdown(dropdown) {
    // 获取触发按钮（右上角的点点按钮）
    const menuButton = document.querySelector('.header-actions-right .header-icon[onclick*="toggleOptionsDropdown"]');
    
    if (dropdown && menuButton) {
        const buttonRect = menuButton.getBoundingClientRect();
        
        // 计算下拉菜单的顶部位置：按钮底部加上少量偏移
        const top = buttonRect.bottom + 5; // 按钮底部位置加5px的间距
        
        // 计算下拉菜单的右侧位置：使下拉菜单的右边缘与按钮的右边缘对齐
        const right = window.innerWidth - buttonRect.right;
        
        dropdown.style.position = 'fixed';
        dropdown.style.top = top + 'px';
        dropdown.style.right = right + 'px';
        dropdown.style.left = 'auto'; // 确保left是auto，避免冲突
        dropdown.style.bottom = 'auto'; // 确俯bottom是auto
        dropdown.style.zIndex = '9999'; // 确保在最上层
        dropdown.style.transform = 'none'; // 移除可能的transform样式
    }
}

// 搜索框旁边筛选下拉菜单定位
function positionSortFilterDropdown(dropdown) {
    // 获取搜索框
    const searchInput = document.querySelector('input[name="searchKey"]');
    
    if (dropdown && searchInput) {
        const searchRect = searchInput.getBoundingClientRect();
        
        // 计算下拉菜单的顶部位置：搜索框底部加上少量偏移
        const top = searchRect.bottom + 5; 
        
        // 计算下拉菜单的左侧位置：对齐搜索框的左边缘
        const left = searchRect.left;
        
        dropdown.style.position = 'fixed';
        dropdown.style.top = top + 'px';
        dropdown.style.left = left + 'px';
        dropdown.style.right = 'auto';
        dropdown.style.bottom = 'auto';
        dropdown.style.zIndex = '9999';
        dropdown.style.transform = 'none';
    }
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(event) {
    // 关闭选项菜单
    const optionsDropdown = document.getElementById('optionsDropdown');
    const optionsTrigger = event.target.closest('[onclick*="toggleOptionsDropdown"]');
    if (!optionsTrigger && optionsDropdown && optionsDropdown.classList.contains('show')) {
        optionsDropdown.classList.remove('show');
    }
    
    // 关闭搜索框旁的筛选菜单
    const sortFilterDropdown = document.getElementById('sortFilterDropdown');
    const sortFilterTrigger = event.target.closest('[onclick*="toggleSortFilterDropdown"]');
    if (!sortFilterTrigger && sortFilterDropdown && sortFilterDropdown.classList.contains('show')) {
        sortFilterDropdown.classList.remove('show');
    }
    
    // 关闭排序菜单（兼容旧代码）
    const sortDropdown = document.getElementById('sortDropdown');
    const sortTrigger = event.target.closest('[onclick*="toggleSortDropdown"]');
    if (!sortTrigger && sortDropdown && sortDropdown.classList.contains('show')) {
        sortDropdown.classList.remove('show');
    }
});

// 管理员清空回复确认
function confirmClearReplies(url) {
    if (confirm('请确认清空操作，此操作不可恢复！\n\n确定要清空该用户的所有回复吗？')) {
        window.location.href = url;
    }
}

// 处理分页按钮点击事件
function initializePaginationButtons() {
    // 隐藏旧版分页控件
    const btBox = document.querySelector('.bt-box');
    const showPage = document.querySelector('.showpage');
    if (btBox) {
        btBox.style.display = 'none';
    }
    if (showPage) {
        showPage.style.display = 'none';
    }
    
    // 为上一页按钮添加点击事件
    const prevBtn = document.getElementById('prevPageBtn');
    if (prevBtn && !prevBtn.disabled) {
        prevBtn.addEventListener('click', function() {
            // 获取当前页码并减一
            const currentPage = {{Pagination.CurrentPage}};
            if (currentPage > 1) {
                navigateToPage(currentPage - 1);
            }
        });
    }
    
    // 生成页码按钮
    generatePageButtons();
    
    // 为下一页按钮添加点击事件
    const nextBtn = document.getElementById('nextPageBtn');
    if (nextBtn && !nextBtn.disabled) {
        nextBtn.addEventListener('click', function() {
            // 获取当前页码并加一
            const currentPage = {{Pagination.CurrentPage}};
            const totalPages = {{Pagination.TotalPages}};
            if (currentPage < totalPages) {
                navigateToPage(currentPage + 1);
            }
        });
    }
}

// 生成页码按钮
function generatePageButtons() {
    // 获取分页容器和下一页按钮
    const container = document.getElementById('pagination-container');
    const nextButton = document.getElementById('nextPageBtn');
    if (!container || !nextButton) return;
    
    const currentPage = {{Pagination.CurrentPage}};
    const totalPages = {{Pagination.TotalPages}};
    
    // 如果总页数小于等于1，不需要生成页码按钮
    if (totalPages <= 1) return;
    
    // 确定要显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    
    // 调整范围确保显示5个页码按钮（如果有足够的页面）
    if (endPage - startPage < 4 && totalPages > 5) {
        if (startPage === 1) {
            endPage = Math.min(5, totalPages);
        } else if (endPage === totalPages) {
            startPage = Math.max(1, totalPages - 4);
        }
    }
    
    // 清除原有的页码按钮（如果有）
    const existingButtons = document.querySelectorAll('.page-number-btn');
    existingButtons.forEach(button => button.remove());
    
    // 创建所有页码按钮
    // 先生成页码按钮并按照正确顺序插入
    // 注意：如果是当前页，用主题色高亮显示
    for (let i = startPage; i <= endPage; i++) {
        const pageButton = document.createElement('button');
        pageButton.className = `w-10 h-10 rounded-full ${i === currentPage ? 'bg-primary text-white font-medium' : 'bg-white border border-border-normal text-text-secondary hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5'} flex items-center justify-center transition-all duration-200 cursor-pointer page-number-btn`;
        pageButton.textContent = i.toString();
        pageButton.setAttribute('data-page', i);
        pageButton.addEventListener('click', function() {
            if (i !== currentPage) {
                navigateToPage(i);
            }
        });
        
        // 直接插入到下一页按钮前
        container.insertBefore(pageButton, nextButton);
    }
}

// 跳转到指定页面
function navigateToPage(page) {
    // 构建URL
    const baseUrl = window.location.href.split('?')[0];
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('page', page);
    window.location.href = baseUrl + '?' + urlParams.toString();
}
</script>