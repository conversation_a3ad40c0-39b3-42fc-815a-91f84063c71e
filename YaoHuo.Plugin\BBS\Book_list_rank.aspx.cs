using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    public class Book_List_Rank : MyPageWap
    {
        private const string CACHE_KEY_PREFIX = "UserRankList_";
        private const int CACHE_DURATION_HOURS = 24;

        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string CurrentlinkURL = "";

        public string linkTOP = "";

        public string condition = "";

        public string stype = "";

        public string stypename = "";

        public string ERROR = "";

        public string stypelink = "";

        public string type = "";

        public string key = "";

        public StringBuilder strhtml = new StringBuilder();

        public List<user_Model> listVo = null;

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        public string downLink = "";

        public int CurrentUserRank { get; private set; }

        /// <summary>
        /// 根据排行类型获取对应的数据库字段名
        /// </summary>
        /// <param name="rankType">排行类型</param>
        /// <returns>数据库字段名</returns>
        private string GetOrderFieldByType(string rankType)
        {
            switch (rankType)
            {
                case "0": return "bbsCount";     // 发帖排行
                case "1": return "bbsreCount";   // 回复排行
                case "2": return "money";        // 妖晶排行
                case "3": return "expr";         // 经验排行
                case "4": return "zoneCount";    // 人气排行
                case "5": return "TJCount";      // 推荐排行
                default: return "bbsCount";      // 默认按发帖排行
            }
        }

        private int GetUserRank(string userId, string rankType)
        {
            if (string.IsNullOrEmpty(userId)) return 0;

            string cacheKey = $"UserRank_{siteid}_{userId}_{rankType}";

            // 尝试从缓存获取
            object cachedRank = HttpRuntime.Cache[cacheKey];
            if (cachedRank != null)
                return (int)cachedRank;

            string orderField = GetOrderFieldByType(rankType);
            if (string.IsNullOrEmpty(orderField))
                return 0;

            string sql = $@"
                SELECT rank 
                FROM (
                    SELECT userid, 
                           ROW_NUMBER() OVER(ORDER BY {orderField} DESC) as rank
                    FROM [user]
                    WHERE siteid = {siteid}
                ) t 
                WHERE userid = {userId}";

            try
            {
                using (var conn = new SqlConnection(PubConstant.GetConnectionString(string_10)))
                {
                    conn.Open();
                    object result = new SqlCommand(sql, conn).ExecuteScalar();

                    if (result != null && result != DBNull.Value)
                    {
                        int rank = Convert.ToInt32(result);
                        HttpRuntime.Cache.Insert(cacheKey, rank, null,
                            DateTime.Now.AddHours(1),
                            System.Web.Caching.Cache.NoSlidingExpiration);
                        return rank;
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误日志
                return 0;
            }

            return 0;
        }

        public List<user_Model> GetCachedUserList(user_BLL user_BLL)
        {
            // 构建缓存键
            string cacheKey = $"{CACHE_KEY_PREFIX}_{siteid}_{stype}_{CurrentPage}_{pageSize}";

            // 尝试从缓存获取数据
            List<user_Model> cachedList = HttpRuntime.Cache[cacheKey] as List<user_Model>;

            if (cachedList != null)
            {
                return cachedList;
            }

            // 如果缓存中没有，则从数据库获取
            List<user_Model> freshList = null;

            if (stype == "0")
            {
                freshList = user_BLL.GetUserListVo(pageSize, CurrentPage, condition, "*", "bbsCount", total, 1L);
            }
            else if (stype == "1")
            {
                freshList = user_BLL.GetUserListVo(pageSize, CurrentPage, condition, "*", "bbsreCount", total, 1L);
            }
            else if (stype == "2")
            {
                freshList = user_BLL.GetUserListVo(pageSize, CurrentPage, condition, "*", "money", total, 1L);
            }
            else if (stype == "3")
            {
                freshList = user_BLL.GetUserListVo(pageSize, CurrentPage, condition, "*", "expr", total, 1L);
            }
            else if (stype == "4")
            {
                freshList = user_BLL.GetUserListVo(pageSize, CurrentPage, condition, "*", "zoneCount", total, 1L);
            }
            else if (stype == "5")
            {
                freshList = user_BLL.GetUserListVo(pageSize, CurrentPage, condition, "*", "TJCount", total, 1L);
            }

            // 将数据存入缓存
            if (freshList != null)
            {
                HttpRuntime.Cache.Insert(
                    cacheKey,
                    freshList,
                    null,
                    DateTime.Now.AddHours(CACHE_DURATION_HOURS),
                    System.Web.Caching.Cache.NoSlidingExpiration
                );
            }

            return freshList;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            downLink = WapTool.GetArryString(classVo.smallimg, '|', 19).Trim().Replace("[stype]", stype);
            condition = " siteid=" + siteid;
            stype = GetRequestValue("stype");
            if (!WapTool.IsNumeric(stype))
            {
                stype = "0";
            }
            try
            {
                if (classVo.ismodel < 1L)
                {
                    pageSize = siteVo.MaxPerPage_Default;
                }
                else
                {
                    pageSize = classVo.ismodel;
                }
                user_BLL user_BLL = new user_BLL(string_10);
                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    total = user_BLL.GetListCount(condition);
                }
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/Book_List_Rank.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total + "&amp;stype=" + stype;
                CurrentlinkURL = linkURL;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL);

                // 获取当前用户排名
                if (userVo != null)
                {
                    CurrentUserRank = GetUserRank(userVo.userid.ToString(), stype);
                }

                // 使用缓存方法替换原有的数据获取逻辑
                listVo = GetCachedUserList(user_BLL);
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}