<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回帖列表</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        /* CSS自定义属性 (Design Tokens) */
        :root {
            /* 主色系 */
            --color-primary: #58b4b0;
            --color-primary-dark: #4a9c98;
            --color-primary-light: #7cd0cb;
            --color-primary-alpha-10: rgba(88, 180, 176, 0.1);
            --color-primary-alpha-30: rgba(88, 180, 176, 0.3);
            --color-primary-alpha-05: rgba(88, 180, 176, 0.05);
            
            /* 文本颜色 */
            --color-text-primary: #1f2937;
            --color-text-secondary: #6b7280;
            --color-text-tertiary: #4b5563;
            --color-text-light: #9ca3af;
            --color-text-white: #ffffff;
            --color-link: #3d68a8;
            
            /* 状态颜色 */
            --color-success: #10b981;
            --color-danger: #dc2626;
            --color-danger-dark: #b91c1c;
            --color-warning: #d97706;
            --color-warning-light: #eab308;
            --color-error: #ef4444;
            --color-info: #3b82f6;
            
            /* 背景颜色 */
            --color-bg-primary: #f9fafb;
            --color-bg-white: #ffffff;
            --color-bg-gray-50: #f9fafb;
            --color-bg-gray-100: #f3f4f6;
            
            /* 边框颜色 */
            --color-border-light: #f3f4f6;
            --color-border-normal: #e5e7eb;
            --color-border-dark: #d1d5db;
            
            /* 特殊背景色 */
            --color-bg-vip: #fef2f2;
            --color-bg-admin: #fef3c7;
            --color-bg-medal: #fef3c7;
            --color-bg-error: #fee2e2;
            --color-bg-info: #e0f2fe;
            
            /* 透明度 */
            --color-white-alpha-10: rgba(255, 255, 255, 0.1);
            --color-white-alpha-20: rgba(255, 255, 255, 0.2);
            --color-white-alpha-30: rgba(255, 255, 255, 0.3);
            --color-black-alpha-50: rgba(0, 0, 0, 0.5);
            --color-black-alpha-70: rgba(0, 0, 0, 0.7);
            
            /* 字体大小 */
            --font-size-xs: 0.75rem;        /* 12px */
            --font-size-sm: 0.875rem;       /* 14px */
            --font-size-base: 1rem;         /* 16px */
            --font-size-lg: 1.125rem;       /* 18px */
            --font-size-xl: 1.25rem;        /* 20px */
            --font-size-2xl: 1.5rem;        /* 24px */
            --font-size-3xl: 1.875rem;      /* 30px */
            
            /* 字体权重 */
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            
            /* 间距系统 (基于4px网格) */
            --spacing-0: 0;
            --spacing-0-5: 0.125rem;        /* 2px */
            --spacing-1: 0.25rem;           /* 4px */
            --spacing-1-5: 0.375rem;        /* 6px */
            --spacing-2: 0.5rem;            /* 8px */
            --spacing-2-5: 0.625rem;        /* 10px */
            --spacing-3: 0.75rem;           /* 12px */
            --spacing-4: 1rem;              /* 16px */
            --spacing-5: 1.25rem;           /* 20px */
            --spacing-6: 1.5rem;            /* 24px */
            --spacing-8: 2rem;              /* 32px */
            --spacing-10: 2.5rem;           /* 40px */
            --spacing-12: 3rem;             /* 48px */
            --spacing-16: 4rem;             /* 64px */
            --spacing-20: 5rem;             /* 80px */
            
            /* 圆角 */
            --radius-none: 0;
            --radius-sm: 0.25rem;           /* 4px */
            --radius-base: 0.375rem;        /* 6px */
            --radius-md: 0.5rem;            /* 8px */
            --radius-lg: 0.75rem;           /* 12px */
            --radius-xl: 1rem;              /* 16px */
            --radius-full: 9999px;          /* 胶囊形 */
            --radius-circle: 50%;           /* 圆形 */
            
            /* 阴影 */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
            
            /* Z-index层级 */
            --z-dropdown: 10;
            --z-modal: 1000;
            --z-toast: 1001;
            
            /* 过渡时间 */
            --transition-fast: 0.15s;
            --transition-base: 0.2s;
            --transition-slow: 0.3s;
            --transition-extra-slow: 1.2s;
            
            /* 过渡函数 */
            --ease-out: cubic-bezier(0, 0, 0.2, 1);
            --ease-in: cubic-bezier(0.4, 0, 1, 1);
            --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
            
            /* 图标尺寸 */
            --icon-xs: 0.875rem;            /* 14px */
            --icon-sm: 1rem;                /* 16px */
            --icon-base: 1.25rem;           /* 20px */
            --icon-lg: 1.5rem;              /* 24px */
            --icon-xl: 2rem;                /* 32px */
            --icon-2xl: 2.5rem;             /* 40px */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #E8E8E8;
            line-height: 1.5;
            box-shadow: 0 2px 1px -1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12);
            padding-bottom: 1rem;
        }

        .container {
            max-width: 720px;
            margin: 0 auto;
            background-color: var(--color-bg-primary);
            min-height: 100vh;
            padding-bottom: var(--spacing-20);
        }

        /* 顶部导航栏 */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            max-width: 720px;
            z-index: var(--z-dropdown);
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            color: var(--color-text-white);
            box-shadow: var(--shadow-md);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-4);
        }

        .header-icon {
            width: var(--spacing-8);
            height: var(--spacing-8);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: var(--radius-base);
            transition: background-color var(--transition-base);
        }

        .header-icon:hover {
            background-color: var(--color-white-alpha-10);
        }

        .header-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-medium);
        }

        /* 搜索框样式 - 从friends.html中采用 */
        .search-container {
            position: fixed;
            top: 56px; 
            width: 100%;
            max-width: 720px;
            background-color: var(--color-bg-white);
            padding: var(--spacing-4);
            z-index: var(--z-dropdown); 
            border-bottom: 1px solid var(--color-border-light);
        }

        .search-section {
            margin-bottom: 0; 
        }

        .search-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-input {
            width: 100%;
            padding: var(--spacing-3) var(--spacing-12) var(--spacing-3) var(--spacing-4); 
            border: 1px solid var(--color-border-normal);
            border-radius: var(--radius-md);
            font-size: var(--font-size-sm);
            background-color: var(--color-bg-white);
            transition: border-color var(--transition-base), box-shadow var(--transition-base);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px var(--color-primary-alpha-10);
        }

        .search-input::placeholder {
            color: var(--color-text-light);
        }

        .search-action-button {
            position: absolute;
            right: var(--spacing-1); 
            top: 50%;
            transform: translateY(-50%);
            background-color: transparent;
            border: none;
            padding: var(--spacing-2); 
            border-radius: var(--radius-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-text-light);
            transition: background-color var(--transition-base), color var(--transition-base), transform var(--transition-fast);
        }

        .search-action-button:hover {
            background-color: var(--color-primary-alpha-10);
            color: var(--color-primary);
        }
        
        .search-action-button:active {
            transform: translateY(-50%) scale(0.9);
            background-color: var(--color-primary-alpha-30);
        }

        .search-action-button .icon { /* This targets icons *inside* the button if needed */
            width: var(--icon-base); 
            height: var(--icon-base); 
        }


        /* 主内容区域 */
        .main-content {
            padding-top: 124px; 
            padding-bottom: 80px; 
            background-color: #ffffff; 
        }

        /* 回帖项样式 */
        .reply-item {
            padding: var(--spacing-3) var(--spacing-4);
            border-bottom: 1px solid var(--color-border-light);
            background-color: #ffffff; 
        }

        .reply-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-2);
        }

        .reply-title {
            color: var(--color-primary);
            font-weight: var(--font-weight-medium);
        }

        .reply-number {
            color: var(--color-text-light);
            font-size: var(--font-size-sm);
        }

        .reply-content {
            margin-bottom: var(--spacing-2);
        }

        .reply-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .reply-date {
            color: var(--color-text-light);
            font-size: var(--font-size-xs);
            display: flex;
            align-items: center;
        }

        .reply-action {
            width: var(--spacing-8);
            height: var(--spacing-8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-text-light);
        }

        /* 分页导航 */
        .pagination-container {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 720px;
            background-color: var(--color-bg-white);
            border-top: 1px solid var(--color-border-light);
            padding: var(--spacing-4);
            z-index: var(--z-dropdown);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--spacing-3);
        }

        .page-button {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-circle);
            background-color: var(--color-bg-gray-100);
            color: var(--color-text-tertiary);
            cursor: pointer;
            font-weight: var(--font-weight-medium);
        }

        .page-button.active {
            background-color: var(--color-primary);
            color: var(--color-text-white);
        }

        .page-button:hover:not(.active) {
            background-color: var(--color-bg-gray-50);
        }

        .page-info {
            text-align: center;
            color: var(--color-text-secondary);
            font-size: var(--font-size-sm);
            margin-top: var(--spacing-2);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                max-width: 100%;
            }
            
            .header, .search-container, .pagination-container {
                max-width: 100%;
            }
        }

        /* 修复Remix图标 */
        :where([class^="ri-"])::before { 
            content: "\f3c2"; 
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 - 保留主文件的header结构 -->
        <div class="header">
            <div class="header-content">
                <div class="header-icon" onclick="history.back()">
                    <i data-lucide="arrow-left" class="icon-lg"></i>
                </div>
                <div class="header-title">回帖列表</div>
                <div class="header-icon"> 
                    <i data-lucide="filter" class="icon-lg"></i>
                </div>
            </div>
        </div>

        <!-- 搜索框 - 采用friends.html的结构和样式 -->
        <div class="search-container">
            <div class="search-section">
                <div class="search-wrapper">
                    <input type="text" placeholder="多个关键词用空格隔开" class="search-input">
                    <button class="search-action-button" id="search-button">
                        <i data-lucide="search" class="icon"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 主内容区域 - 参考文件的结构 -->
        <div class="main-content">
            <!-- 回帖项 1 -->
            <div class="reply-item">
                <div class="reply-header">
                    <div class="reply-title">红尘客栈 (1075)</div>
                    <div class="reply-number">#1642</div>
                </div>
                <div class="reply-content">吃了</div>
                <div class="reply-footer">
                    <div class="reply-date">
                        <i class="ri-time-line ri-sm" style="margin-right: 4px;"></i>
                        2025-04-04 20:06
                    </div>
                    <div class="reply-action">
                        <i class="ri-eye-line"></i>
                    </div>
                </div>
            </div>
            
            <!-- 回帖项 2 -->
            <div class="reply-item">
                <div class="reply-header">
                    <div class="reply-title">红尘客栈 (1075)</div>
                    <div class="reply-number">#1641</div>
                </div>
                <div class="reply-content">吃了</div>
                <div class="reply-footer">
                    <div class="reply-date">
                        <i class="ri-time-line ri-sm" style="margin-right: 4px;"></i>
                        2025-02-08 23:46
                    </div>
                    <div class="reply-action">
                        <i class="ri-eye-line"></i>
                    </div>
                </div>
            </div>

            <!-- 回帖项 3 -->
            <div class="reply-item">
                <div class="reply-header">
                    <div class="reply-title">红尘客栈 (1075)</div>
                    <div class="reply-number">#1640</div>
                </div>
                <div class="reply-content">吃</div>
                <div class="reply-footer">
                    <div class="reply-date">
                        <i class="ri-time-line ri-sm" style="margin-right: 4px;"></i>
                        2025-02-21 19:40
                    </div>
                    <div class="reply-action">
                        <i class="ri-eye-line"></i>
                    </div>
                </div>
            </div>
            
            <!-- 回帖项 4 -->
            <div class="reply-item">
                <div class="reply-header">
                    <div class="reply-title">红尘客栈 (1075)</div>
                    <div class="reply-number">#1639</div>
                </div>
                <div class="reply-content">吃了</div>
                <div class="reply-footer">
                    <div class="reply-date">
                        <i class="ri-time-line ri-sm" style="margin-right: 4px;"></i>
                        2025-02-14 17:21
                    </div>
                    <div class="reply-action">
                        <i class="ri-eye-line"></i>
                    </div>
                </div>
            </div>

            <!-- 回帖项 5 -->
            <div class="reply-item">
                <div class="reply-header">
                    <div class="reply-title">红尘客栈 (1075)</div>
                    <div class="reply-number">#1638</div>
                </div>
                <div class="reply-content">新年快乐</div>
                <div class="reply-footer">
                    <div class="reply-date">
                        <i class="ri-time-line ri-sm" style="margin-right: 4px;"></i>
                        2025-02-07 19:24
                    </div>
                    <div class="reply-action">
                        <i class="ri-eye-line"></i>
                    </div>
                </div>
            </div>

            <!-- 回帖项 6 -->
            <div class="reply-item">
                <div class="reply-header">
                    <div class="reply-title">红尘客栈 (1075)</div>
                    <div class="reply-number">#1637</div>
                </div>
                <div class="reply-content">硬件为王，软件为辅。</div>
                <div class="reply-footer">
                    <div class="reply-date">
                        <i class="ri-time-line ri-sm" style="margin-right: 4px;"></i>
                        2025-08-18 14:12
                    </div>
                    <div class="reply-action">
                        <i class="ri-eye-line"></i>
                    </div>
                </div>
            </div>

            <!-- 回帖项 7 -->
            <div class="reply-item">
                <div class="reply-header">
                    <div class="reply-title">红尘客栈 (1075)</div>
                    <div class="reply-number">#1636</div>
                </div>
                <div class="reply-content">感谢分享</div>
                <div class="reply-footer">
                    <div class="reply-date">
                        <i class="ri-time-line ri-sm" style="margin-right: 4px;"></i>
                        2025-08-01 03:23
                    </div>
                    <div class="reply-action">
                        <i class="ri-eye-line"></i>
                    </div>
                </div>
            </div>

            <!-- 回帖项 8 -->
            <div class="reply-item">
                <div class="reply-header">
                    <div class="reply-title">红尘客栈 (1075)</div>
                    <div class="reply-number">#1635</div>
                </div>
                <div class="reply-content">感谢分享。</div>
                <div class="reply-footer">
                    <div class="reply-date">
                        <i class="ri-time-line ri-sm" style="margin-right: 4px;"></i>
                        2025-07-30 03:03
                    </div>
                    <div class="reply-action">
                        <i class="ri-eye-line"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页导航 - 参考文件的结构 -->
        <div class="pagination-container">
            <div class="pagination">
                <div class="page-button">
                    <i class="ri-arrow-left-s-line"></i>
                </div>
                
                <div class="page-button active">1</div>
                <div class="page-button">2</div>
                <div class="page-button">3</div>
                <div class="page-button">4</div>
                <div class="page-button">5</div>
                
                <div class="page-button">
                    <i class="ri-arrow-right-s-line"></i>
                </div>
            </div>
            
            <div class="page-info">
                第 1/110 页，共 1642 条
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        document.addEventListener('DOMContentLoaded', function() {
            // 搜索按钮交互
            const searchButton = document.getElementById('search-button');
            const searchInput = document.querySelector('.search-input');
            
            if (searchButton && searchInput) { 
                searchButton.addEventListener('click', function() {
                    if (searchInput.value.trim()) {
                        console.log('搜索:', searchInput.value.trim());
                    }
                });
                
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && this.value.trim()) {
                        console.log('搜索:', this.value.trim());
                    }
                });
            }
        });

        // 分页按钮交互
        document.addEventListener('DOMContentLoaded', function() {
            const paginationButtons = document.querySelectorAll('.page-button');
            
            paginationButtons.forEach(button => {
                button.addEventListener('click', function() {
                    paginationButtons.forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    if (!this.querySelector('i')) {
                        this.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>