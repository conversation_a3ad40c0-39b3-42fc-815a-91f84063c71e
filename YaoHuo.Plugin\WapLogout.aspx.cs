﻿using System;
using System.Data;
using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin
{
	public class WapLogout : MyPageWap
    {
		public string isGO = "";

		public static string _InstanceName = PubConstant.GetAppString("InstanceName");

		public static string _ConnStr = PubConstant.GetConnectionString(_InstanceName);

		protected void Page_Load(object sender, EventArgs e)
		{
			IsLogin(userid, "waplogout.aspx?siteid=" + siteid);
			isGO = GetRequestValue("isGO");
			if (isGO == "OK")
			{
				DbHelperSQL.ExecuteNonQuery(_ConnStr, CommandType.Text, "delete from [fcount] where userid=" + userid);
				DbHelperSQL.ExecuteNonQuery(_ConnStr, CommandType.Text, "update [user] set SidTimeOut=null where userid=" + userid);
				string text = base.Request.ServerVariables["HTTP_HOST"].Split('.')[0];
				base.Response.Cookies["sid" + text].Value = null;
				base.Response.Cookies["GET" + userid].Value = null;
				Session["sid1"] = "";
				Session["check_userid"] = null;
				Session["KL_LOGIN_IS_ADMIN" + userid] = null;
				base.Response.Redirect(http_start + "wapindex.aspx?siteid=" + siteid);
			}
		}
	}
}