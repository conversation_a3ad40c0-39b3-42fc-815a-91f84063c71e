﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.Album
{
    public class admin_WAPadd : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");
        private readonly FileUploadHelper _fileUploadHelper;

        public string KL_NotDownAndUpload = PubConstant.GetAppString("KL_NotDownAndUpload");
        public List<class_Model> classList = new List<class_Model>();
        public List<wap_albumSubject_Model> smallTypeList = null;
        public string action = "";
        public string id = "";
        public string page = "";
        public string INFO = "";
        public string ERROR = "";
        public string whickOK = "";
        public string toclassid = "";
        public string smalltypeid = "";
        public string book_title = "";
        public string book_content = "";
        public string ishidden = "0";
        public string book_img = "";
        public string swidth = "";
        public string sheight = "";
        public int num = 1;

        public admin_WAPadd()
        {
            _fileUploadHelper = new FileUploadHelper(this);
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            page = GetRequestValue("page");
            toclassid = GetRequestValue("toclassid");
            IsLogin(userid, GetUrlQueryString());
            if (GetRequestValue("num") != "")
            {
                num = int.Parse(GetRequestValue("num"));
            }
            smalltypeid = GetRequestValue("smalltypeid");
            if (smalltypeid == "")
            {
                smalltypeid = "0";
            }
            class_BLL class_BLL = new class_BLL(string_10);
            classList = class_BLL.GetFromPathList(long.Parse(siteid), "album/index.aspx");
            string strWhere = " siteid = " + siteid + " and userid=" + userid;
            wap_albumSubject_BLL wap_albumSubject_BLL = new wap_albumSubject_BLL(string_10);
            smallTypeList = wap_albumSubject_BLL.GetListVo(100L, 1L, strWhere, "*", "ordernum", 100L, 0);
            if (action != "gomod")
            {
                return;
            }
            try
            {
                book_title = GetRequestValue("book_title").Trim();
                book_content = GetRequestValue("book_content");
                toclassid = GetRequestValue("toclassid");
                if (toclassid == "")
                {
                    toclassid = "0";
                }
                swidth = GetRequestValue("swidth");
                sheight = GetRequestValue("sheight");
                ishidden = GetRequestValue("ishidden");
                if (book_title.Length < 3)
                {
                    INFO = "NULL";
                    return;
                }
                if (book_title.IndexOf("$(") >= 0)
                {
                    INFO = "ERR_FORMAT";
                    return;
                }
                if (WapTool.IsLockuser(siteid, userid, classid) > -1L)
                {
                    INFO = "LOCK";
                    return;
                }
                var uploadResult = _fileUploadHelper.ProcessFileUpload(
                    Request.Files,
                    null,
                    siteid,
                    userid,
                    "相册上传",
                    siteVo.UpFileType,
                    KL_NotDownAndUpload,
                    siteVo.myspace.ToString(),
                    siteVo.sitespace.ToString(),
                    siteVo.MaxFileSize.ToString(),
                    siteVo.isCheck.ToString(),
                    swidth,
                    sheight,
                    WapTool.GetSiteDefault(siteVo.Version, 4),
                    WapTool.GetSiteDefault(siteVo.Version, 12),
                    WapTool.GetSiteDefault(siteVo.Version, 13),
                    WapTool.GetSiteDefault(siteVo.Version, 16),
                    useLegacyFileName: true
                );
                if (!string.IsNullOrEmpty(uploadResult.INFO))
                {
                    INFO = uploadResult.INFO;
                    return;
                }
                if (uploadResult.Attachments.Count > 0)
                {
                    // 直接从物理文件获取大小
                    long totalSizeKB = uploadResult.Attachments.Sum(a =>
                    {
                        var filePath = HttpContext.Current.Server.MapPath(a.book_file);
                        return new FileInfo(filePath).Length / 1024;  // 转换为KB
                    });

                    // 获取第一个附件的信息
                    var firstAttachment = uploadResult.Attachments[0];
                    string originalFilePath = firstAttachment.book_file;
                    string physicalPath = HttpContext.Current.Server.MapPath(originalFilePath);
                    string fileName = Path.GetFileName(originalFilePath);

                    // 处理缩略图
                    string text8 = WapTool.GetSiteDefault(siteVo.Version, 8);
                    string text9 = WapTool.GetSiteDefault(siteVo.Version, 9);
                    if (!WapTool.IsNumeric(text8))
                    {
                        text8 = "130";
                    }
                    if (!WapTool.IsNumeric(text9))
                    {
                        text9 = "0";
                    }
                    if (int.Parse(text8) > 500)
                    {
                        text8 = "500";
                    }
                    if (int.Parse(text8) < 50)
                    {
                        text8 = "50";
                    }
                    if (int.Parse(text9) > 500)
                    {
                        text9 = "500";
                    }

                    // 生成缩略图
                    string thumbnailFileName = "S" + fileName;
                    string thumbnailPath = Path.Combine(Path.GetDirectoryName(physicalPath), thumbnailFileName);
                    if (text9 == "0")
                    {
                        WapTool.MakeThumbnail(physicalPath, thumbnailPath, int.Parse(text8), int.Parse(text9), "W");
                    }
                    else
                    {
                        WapTool.MakeThumbnail(physicalPath, thumbnailPath, int.Parse(text8), int.Parse(text9), "HW");
                    }

                    // 设置缩略图路径
                    book_img = "upload/" + siteid + "/" + WapTool.GetDatePathString() + thumbnailFileName;

                    // 记录缩略图上传日志
                    WapTool.SaveUploadFileToLog(siteid, userid, "1", "相册上传",
                        Path.GetExtension(fileName),
                        (new FileInfo(thumbnailPath).Length / 1024).ToString(),
                        "album/" + book_img,
                        siteVo.isCheck.ToString());

                    wap_album_Model albumModel = new wap_album_Model
                    {
                        userid = long.Parse(siteid),
                        book_classid = long.Parse(toclassid),
                        book_title = book_title,
                        book_author = userVo.nickname,
                        book_ext = Path.GetExtension(firstAttachment.book_file).Replace(".", ""),
                        book_size = totalSizeKB.ToString(),
                        book_img = book_img,
                        book_file = string.Join("|", uploadResult.Attachments.ConvertAll(a => a.book_file)) + "|",
                        book_content = book_content,
                        book_date = DateTime.Now,
                        ischeck = siteVo.isCheck,
                        ishidden = long.Parse(ishidden),
                        smalltype = long.Parse(smalltypeid),
                        makerid = long.Parse(userid)
                    };
                    wap_album_BLL wap_album_BLL = new wap_album_BLL(string_10);
                    long albumId = wap_album_BLL.Add(albumModel);
                    wap2_attachment_BLL attachmentBLL = new wap2_attachment_BLL(string_10);
                    foreach (var attachment in uploadResult.Attachments)
                    {
                        attachment.book_id = albumId;
                        attachment.book_type = "album";
                        attachmentBLL.Add(attachment);
                    }
                    INFO = "OK";
                    Action_user_doit(8);
                }
                else
                {
                    INFO = "请选择要上传的图片";
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}