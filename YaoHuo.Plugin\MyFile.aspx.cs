﻿using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using System;
using System.Data;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin
{
    public class MyFile : MyPageWap
    {
        public string INFO = "";

        public string ERROR = "";

        public string strHtml = "";

        public string strShowHtml = "";

        public string messagecount = "0";

        public string messageAll = "0";

        public string goodfriend = "0";

        public string type = "";

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";

            // 检查Cookie中的UI偏好
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }

            // 默认使用旧版本
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            // 如果偏好是新版本，尝试使用Handlebars模板
            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars(); // 返回是否成功渲染新版
            }

            return false; // 使用旧版
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 检查是否存在TemplateService
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                if (templateServiceType != null)
                {
                    // 使用反射调用TemplateService
                    var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
                    var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                    if (getViewModeMethod != null && renderPageMethod != null)
                    {
                        string viewMode = (string)getViewModeMethod.Invoke(null, null);
                        if (viewMode == "new")
                        {
                            RenderWithHandlebars();
                            return true; // 成功渲染新版
                        }
                    }
                }

                // 如果Handlebars不可用，记录错误但不回退
                ERROR = "Handlebars模板服务不可用";
                System.Diagnostics.Debug.WriteLine("Handlebars模板服务不可用，继续使用旧版");
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 RenderWithHandlebars 中 Response.End() 的正常行为
                // 表示新版渲染成功并正常终止了线程，这是期望的行为
                System.Diagnostics.Debug.WriteLine("新版渲染成功，线程正常终止");
                return true; // 实际上是成功的
            }
            catch (Exception ex)
            {
                // 记录错误但不回退到静态模板
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                System.Diagnostics.Debug.WriteLine($"模板渲染错误: {ex.Message}，继续使用旧版");
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars()
        {
            // 原有渲染逻辑的备份注释：
            // 旧的渲染方式使用了双重RenderPage调用：
            // 1. 先渲染页面主体 "~/Template/Pages/MyFile.hbs"
            // 2. 再将页面主体作为content传递给主布局 "~/Template/Layouts/MainLayout.hbs"
            // 这种方式已被新的RenderPageWithLayout统一方法替换

            try
            {
                // 1. 构建页面主体所需的数据模型
                var pageModel = BuildUserPageModel(); // 保持BuildUserPageModel方法不变

                // 2. 调用新的 RenderPageWithLayout 方法
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/MyFile.hbs",    // 页面模板路径
                    pageModel,                         // 页面数据模型
                    "个人中心",                        // 页面标题
                    new YaoHuo.Plugin.BBS.Models.HeaderOptionsModel()           // 头部选项 (默认配置，使用Models命名空间)
                                                                                // 移除页面专属CSS引用
                );

                // 3. 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End(); // 使用Response.End()确保页面执行完全终止
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 Response.End() 的正常行为，不需要处理
                // 直接重新抛出，让它正常终止线程
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Handlebars 渲染失败 (MyFile.aspx): {ex.ToString()}");
                // 记录错误但不回退到旧版，让新的TemplateService返回错误HTML信息
                ERROR = "新版界面加载失败: " + WapTool.ErrorToString(ex.ToString());
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}。请联系管理员。</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest(); // 在异常处理中使用CompleteRequest避免嵌套异常
            }
        }

        /// <summary>
        /// 构建用户页面数据模型
        /// </summary>
        private object BuildUserPageModel()
        {
            // 使用匿名对象构建数据模型，避免强类型依赖
            return new
            {
                UserInfo = new
                {
                    UserId = userVo.userid.ToString(),
                    Nickname = userVo.nickname ?? "用户",
                    DisplayName = FilterHtmlTags(WapTool.GetColorNickName(userVo.idname, userVo.nickname, lang, ver)),
                    Level = WapTool.GetLevl(siteVo.lvlNumer, userVo.expr, userVo.money, type),
                    Identity = FilterHtmlTags(WapTool.GetMyID(userVo.idname, lang)),
                    IdentityHtml = WapTool.GetMyID(userVo.idname, lang),
                    EndTime = ProcessEndTime(),
                    Experience = userVo.expr,
                    IsVip = IsVipUser(),
                    HasEndTime = HasValidEndTime()
                },
                Statistics = new
                {
                    MessageCount = messagecount,
                    MessageAll = messageAll,
                    FriendCount = goodfriend,
                    PostCount = userVo.bbsCount.ToString(),
                    ReplyCount = userVo.bbsReCount.ToString(),
                    MessageDisplay = messagecount + "/" + messageAll
                },
                Assets = new
                {
                    Money = userVo.money,
                    BankMoney = userVo.myBankMoney,
                    RMB = userVo.RMB,
                    MoneyName = WapTool.GetSiteMoneyName(siteVo.sitemoneyname, lang),
                    MoneyDisplay = userVo.money.ToString("N0"),
                    BankDisplay = userVo.myBankMoney.ToString("N0"),
                    RMBDisplay = userVo.RMB.ToString("f2"),
                    HasBankMoney = userVo.myBankMoney > 0,
                    HasRMB = userVo.RMB > 0
                },
                Permissions = new
                {
                    ManagerLevel = userVo.managerlvl,
                    AdminDisplay = WapTool.GetIDName(siteid, userid, userVo.managerlvl, lang),
                    IsAdmin = userVo.managerlvl == "00" || userVo.managerlvl == "01",
                    IsSuperAdmin = userVo.managerlvl == "00",
                    HasAdminPermission = userVo.managerlvl != "普通" && !string.IsNullOrEmpty(userVo.managerlvl)
                },
                Medals = new
                {
                    MedalHtml = GetMedalContent(),
                    HasMedals = !string.IsNullOrEmpty(GetMedalContent())
                },
                Links = BuildLinks(),
                SiteInfo = new
                {
                    SiteId = siteid.ToString(),
                    HttpStart = http_start,
                    MoneyName = siteVo.sitemoneyname
                }
            };
        }

        /// <summary>
        /// 过滤HTML标签，提取纯文本
        /// </summary>
        private string FilterHtmlTags(string html)
        {
            if (string.IsNullOrEmpty(html))
                return string.Empty;

            // 移除所有HTML标签
            string result = System.Text.RegularExpressions.Regex.Replace(html, "<.*?>", string.Empty);

            return result;
        }

        /// <summary>
        /// 构建功能链接
        /// </summary>
        private object BuildLinks()
        {
            return new
            {
                MailboxUrl = http_start + "bbs/messagelist.aspx?types=0",
                FriendsUrl = http_start + "bbs/FriendList.aspx?friendtype=0",
                PostsUrl = http_start + "bbs/book_list.aspx?action=search&key=" + userid + "&type=pub",
                RepliesUrl = http_start + "bbs/book_re_my.aspx?touserid=" + userid,
                EditProfileUrl = http_start + "bbs/EditProfile.aspx",
                RechargeUrl = http_start + "chinabank_wap/RMBtoMoney.aspx",
                AccountDetailUrl = http_start + "bbs/banklist.aspx?key=" + userid,
                RMBRechargeUrl = http_start + "chinabank_wap/selbank_wap.aspx",
                RMBDetailUrl = http_start + "chinabank_wap/banklist.aspx?tositeid=" + siteid + "&touserid=" + userid,
                ApplyMedalUrl = "/wapindex.aspx?classid=224",
                BuyMedalUrl = "/wapindex.aspx?classid=226",
                FavoritesUrl = http_start + "bbs/favlist.aspx",
                AlbumUrl = "/album/albumlist.aspx?touserid=" + userid,
                ClanUrl = http_start + "clan/main.aspx",
                BlacklistUrl = http_start + "bbs/FriendList.aspx?friendtype=1",
                AdminUrl = http_start + "admin/basesitemodifywml.aspx",
                SuperAdminUrl = http_start + "admin/basesitemodifywml00.aspx",
                LogoutUrl = http_start + "waplogout.aspx",
                VipUrl = "/bbs/BuyGroup.html",
                ChangePasswordUrl = http_start + "bbs/ModifyPW.aspx",
                ChangeAvatarUrl = http_start + "bbs/ModifyHead.aspx"
            };
        }

        /// <summary>
        /// 处理有效期时间显示
        /// </summary>
        private string ProcessEndTime()
        {
            string endTimeDisplay = WapTool.showIDEndTime(userVo.siteid, userVo.userid, userVo.endTime, lang);

            // 提取日期部分
            if (endTimeDisplay.Contains("有效期至:"))
            {
                if (endTimeDisplay.Contains("无期限"))
                {
                    return "无期限";
                }

                // 使用正则表达式提取日期
                var match = System.Text.RegularExpressions.Regex.Match(endTimeDisplay, @"有效期至:(\d{4}-\d{2}-\d{2})");
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return endTimeDisplay;
        }

        /// <summary>
        /// 判断是否为VIP用户
        /// </summary>
        private bool IsVipUser()
        {
            string identity = WapTool.GetMyID(userVo.idname, lang);

            // 检查身份是否包含VIP相关文本
            bool isVip = identity.Contains("VIP") || identity.Contains("vip");

            // 检查是否含有特定的昵称颜色类型
            if (!isVip && !string.IsNullOrEmpty(userVo.idname))
            {
                string[] vipTypes = new[] { "5", "6", "7", "8", "9", "10", "b1", "b2", "b3", "a1", "a2", "a3", "a4" };
                foreach (var vipType in vipTypes)
                {
                    if (userVo.idname.Contains(vipType))
                        return true;
                }
            }

            return isVip;
        }

        /// <summary>
        /// 判断是否有有效期
        /// </summary>
        private bool HasValidEndTime()
        {
            string endTimeDisplay = WapTool.showIDEndTime(userVo.siteid, userVo.userid, userVo.endTime, lang);
            return !endTimeDisplay.Contains("无期限") && endTimeDisplay.Contains("有效期至:");
        }

        /// <summary>
        /// 获取勋章内容
        /// </summary>
        private string GetMedalContent()
        {
            try
            {
                // 使用WapTool.GetMedal获取勋章HTML
                string medalHtml = WapTool.GetMedal(userVo.userid.ToString(), userVo.moneyname,
                    WapTool.GetSiteDefault(siteVo.Version, 47), wmlVo);

                if (string.IsNullOrEmpty(medalHtml))
                {
                    return "";
                }

                // 直接返回原始勋章HTML，样式由Handlebars模板中的Tailwind类控制
                // 如果未来需要其他转换，可以在这里添加，但不再添加内联style
                return medalHtml;
            }
            catch (Exception)
            {
                // 如果获取失败，返回空字符串
                return "";
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            IsLogin(userid, "myfile.aspx?siteid=" + siteid);
            if (WapTool.GetArryString(siteVo.Version, '|', 54) == "1")
            {
                needPassWordToAdmin();
            }
            type = WapTool.GetSiteDefault(siteVo.Version, 27);

            try
            {
                strHtml = siteVo.siterowremark;
                var appString = PubConstant.GetAppString("InstanceName");
                var connectionString = PubConstant.GetConnectionString(appString);
                var commandText = "select count(id),(select count(id) from wap_message where siteid=" + siteid + " and isnew<>2 and touserid=" + userid + ") from wap_message where siteid=" + siteid + " and isnew=1 and  touserid=" + userid;
                var dataSet = DbHelperSQL.ExecuteDataset(connectionString, CommandType.Text, commandText);
                if (dataSet != null && dataSet.Tables[0].Rows.Count > 0)
                {
                    if (dataSet.Tables[0].Rows[0][0] != null)
                    {
                        messagecount = dataSet.Tables[0].Rows[0][0].ToString();
                    }
                    if (dataSet.Tables[0].Rows[0][1] != null)
                    {
                        messageAll = dataSet.Tables[0].Rows[0][1].ToString();
                    }
                }
                commandText = "select count(id) from wap_friends where friendtype=0 and userid=" + userid;
                dataSet = DbHelperSQL.ExecuteDataset(connectionString, CommandType.Text, commandText);
                if (dataSet != null && dataSet.Tables[0].Rows.Count > 0 && dataSet.Tables[0].Rows[0][0] != null)
                {
                    goodfriend = dataSet.Tables[0].Rows[0][0].ToString();
                }
                addMoneyToMyBank();

                // 在数据准备完成后检查UI偏好
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            VisiteCount("进入我的地盘。");
        }
    }
}