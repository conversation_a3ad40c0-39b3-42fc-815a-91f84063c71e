<!-- 顶部导航栏 -->
<div class="header">
    <div class="header-content">
        <div class="header-icon" id="back-button">
            <i data-lucide="arrow-left" class="w-6 h-6"></i>
        </div>
        <div class="header-title">{{pageTitle}}</div>
        <div class="header-actions-right">
            {{#if HeaderOptions.ShowViewModeToggle}}
            <div class="header-icon dropdown" id="theme-toggle">
                <i data-lucide="brush" class="w-5 h-5"></i>
                <div class="dropdown-menu" id="skin-dropdown">
                    <div class="dropdown-item active">
                        <span>新版</span>
                    </div>
                    <div class="dropdown-item" onclick="setUiPreferenceCookie('old', 30); window.location.reload();">
                        <span>旧版</span>
                    </div>
                </div>
            </div>
            {{/if}}
            {{#if HeaderOptions.CustomButtonIcon}}
            <div class="header-icon" id="custom-header-button" onclick="{{#if HeaderOptions.CustomButtonOnClick}}{{HeaderOptions.CustomButtonOnClick}}{{else}}window.location.href='{{HeaderOptions.CustomButtonLink}}'{{/if}}">
                <i data-lucide="{{HeaderOptions.CustomButtonIcon}}" class="w-5 h-5"></i>
            </div>
            {{/if}}
        </div>
    </div>
</div>

<script>
    // 智能返回按钮功能
    document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.getElementById('back-button');
        
        if (backButton) {
            backButton.addEventListener('click', function() {
                // 简单且兼容性好的解决方案
                smartBack();
            });
        }
    });

    // 智能返回函数
    function smartBack() {
        try {
            // 检查是否有来源页面（referrer）
            const referrer = document.referrer;
            
            // 如果有来源页面且来源页面与当前页面不同
            if (referrer && referrer !== window.location.href) {
                // 检查来源页面是否为同域（提高兼容性）
                const currentDomain = window.location.origin;
                if (referrer.startsWith(currentDomain) || referrer.startsWith('http')) {
                    // 尝试返回上一页
                    window.history.back();
                    
                    // 设置一个超时，如果0.5秒后仍在当前页面，则跳转到首页
                    // 这样可以处理某些浏览器history.back()不生效的情况
                    setTimeout(function() {
                        if (window.location.href.indexOf('myfile.aspx') !== -1) {
                            window.location.href = '/';
                        }
                    }, 500);
                    
                    return;
                }
            }
            
            // 没有有效的来源页面，直接跳转到首页
            window.location.href = '/';
            
        } catch (error) {
            // 如果出现任何错误，默认跳转到首页
            console.log('Back button error:', error);
            window.location.href = '/';
        }
    }

    // 皮肤下拉菜单交互
    document.addEventListener('DOMContentLoaded', function() {
        const themeToggle = document.getElementById('theme-toggle');
        const skinDropdown = document.getElementById('skin-dropdown');
        
        if (themeToggle) {
            // 点击皮肤图标显示/隐藏下拉菜单
            themeToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // 检查当前下拉菜单是否已经展开
                const isCurrentlyOpen = skinDropdown.classList.contains('show');
                
                if (isCurrentlyOpen) {
                    // 如果当前菜单已展开，直接关闭它
                    skinDropdown.classList.remove('show');
                } else {
                    // 如果当前菜单未展开，先关闭其他所有下拉菜单，然后展开当前菜单
                    if (typeof closeAllDropdowns === 'function') {
                        closeAllDropdowns();
                    }
                    skinDropdown.classList.add('show');
                }
            });
            
            // 点击页面其他区域关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!themeToggle.contains(e.target)) {
                    // 使用通用关闭函数，如果存在的话
                    if (typeof closeAllDropdowns === 'function') {
                        closeAllDropdowns();
                    } else {
                        skinDropdown.classList.remove('show');
                    }
                }
            });
            
            // 点击新版选项
            const activeItem = document.querySelector('.dropdown-item.active');
            if (activeItem) {
                activeItem.addEventListener('click', function(e) {
                    e.stopPropagation();
                    skinDropdown.classList.remove('show');
                    showToast('当前已是新版界面');
                });
            }
        }
    });

    // 显示提示消息
    function showToast(message) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.className = 'fixed bottom-5 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2.5 px-5 rounded z-[1000]';
        toast.style.opacity = '0';
        toast.style.transition = 'opacity 0.3s ease';
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
</script> 