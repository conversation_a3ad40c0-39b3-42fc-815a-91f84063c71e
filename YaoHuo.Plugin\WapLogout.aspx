﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="WapLogout.aspx.cs" Inherits="YaoHuo.Plugin.WapLogout" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
StringBuilder strhtml = new StringBuilder();                                                                                                                                                                     
Response.Write(WapTool.showTop(this.GetLang("安全退出|安全退出|Security Exit"), wmlVo));
strhtml.Append("<div class=\"subtitle\">安全退出</div>");
strhtml.Append("<div class=\"content\"><div class=\"bt1\"><a id='logout' href=\"" + this.http_start + "waplogout.aspx?siteid=" + this.siteid + "&amp;isGO=OK\">确定要安全退出？</a></div>");
strhtml.Append("<div class=\"tip\"><b>提示:安全退出后需要重新登录！</b></div> ");
strhtml.Append("</div><script>document.getElementById('logout').addEventListener('click', function(e) { e.preventDefault(); var cookies = document.cookie.split(\";\"); for (var i = 0; i < cookies.length; i++) { var cookie = cookies[i]; var eqPos = cookie.indexOf(\"=\"); var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie; document.cookie = name + \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT\"; } window.location.href = \"/wapindex.aspx?sid=-2\"; });</script>");
string isWebHtml = this.ShowWEB_view(this.classid);
if (isWebHtml != "")
{
    Response.Clear();
    Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
    Response.End();
}
Response.Write(strhtml);
Response.Write(WapTool.showDown(wmlVo));
%>