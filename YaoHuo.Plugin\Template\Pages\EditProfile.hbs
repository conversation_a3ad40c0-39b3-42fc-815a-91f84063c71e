{{#if Message.HasMessage}}
<div class="message {{Message.Type}}">
    {{Message.Content}}
</div>
{{/if}}

<form id="profile-form" action="{{FormData.ActionUrl}}" method="post">
    <!-- 论坛资料 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i data-lucide="message-circle" class="card-icon"></i>
                论坛资料
            </h2>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label class="form-label">昵称</label>
                <input type="text" 
                       name="tonickname" 
                       class="form-input" 
                       maxlength="15" 
                       value="{{FormData.ForumInfo.Nickname}}" 
                       placeholder="请输入昵称">
                <div class="form-hint">{{FormData.ForumInfo.NicknameHint}}</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">个性签名</label>
                <input type="text" 
                       name="remark" 
                       class="form-input" 
                       maxlength="15" 
                       value="{{FormData.ForumInfo.Signature}}">
            </div>
        </div>
    </div>

    <!-- 联系方式 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i data-lucide="mail" class="card-icon"></i>
                联系方式
            </h2>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label class="form-label">手机</label>
                <input type="tel" 
                       name="mobile" 
                       class="form-input" 
                       maxlength="11" 
                       pattern="[0-9]*" 
                       value="{{FormData.ContactInfo.Mobile}}" 
                       placeholder="非登录手机号">
            </div>
            
            <div class="form-group">
                <label class="form-label">邮箱</label>
                <input type="email" 
                       name="email" 
                       class="form-input" 
                       maxlength="30" 
                       value="{{FormData.ContactInfo.Email}}">
            </div>
            
            <div class="form-group">
                <label class="form-label">QQ号</label>
                <input type="text" 
                       name="qq" 
                       class="form-input" 
                       maxlength="11" 
                       pattern="[0-9]*" 
                       value="{{FormData.ContactInfo.QQ}}">
            </div>
        </div>
    </div>

    <!-- 个人信息 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i data-lucide="user-round" class="card-icon"></i>
                个人信息
            </h2>
        </div>
        <div class="card-body">
            <!-- 基本信息（默认显示） -->
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">年龄</label>
                    <input type="number" 
                           name="age" 
                           class="form-input" 
                           min="10" 
                           max="99" 
                           value="{{#if FormData.PersonalInfo.Age}}{{FormData.PersonalInfo.Age}}{{/if}}">
                </div>
                
                <div class="form-group">
                    <label class="form-label">爱好</label>
                    <input type="text" 
                           name="aihao" 
                           class="form-input" 
                           maxlength="10" 
                           value="{{FormData.PersonalInfo.Hobby}}">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">城市</label>
                    <input type="text" 
                           name="city" 
                           class="form-input" 
                           maxlength="8" 
                           value="{{FormData.PersonalInfo.City}}">
                </div>
                
                <div class="form-group">
                    <label class="form-label">职业</label>
                    <input type="text" 
                           name="zhiye" 
                           class="form-input" 
                           maxlength="5" 
                           value="{{FormData.PersonalInfo.Occupation}}">
                </div>
            </div>
            
            <!-- 展开更多按钮 -->
            <div class="expand-toggle">
                <button type="button" class="expand-btn" id="expand-more-btn">
                    <i data-lucide="chevron-down" class="icon mr-2"></i>
                    <span class="expand-btn-text">展开更多</span>
                </button>
            </div>
            
            <!-- 详细信息（默认隐藏） -->
            <div class="more-fields" id="more-fields" style="display: none;">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">身高 (cm)</label>
                        <input type="number" 
                               name="shenggao" 
                               class="form-input" 
                               min="100" 
                               max="250" 
                               value="{{FormData.PersonalInfo.Height}}">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">体重 (kg)</label>
                        <input type="number" 
                               name="tizhong" 
                               class="form-input" 
                               min="30" 
                               max="300" 
                               value="{{FormData.PersonalInfo.Weight}}">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">星座</label>
                        <select name="xingzuo" class="form-select">
                            {{#each OptionLists.ZodiacOptions}}
                            <option value="{{Value}}"{{#if Selected}} selected{{/if}}>{{Text}}</option>
                            {{/each}}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">婚否</label>
                        <select name="fenfuo" class="form-select">
                            {{#each OptionLists.MaritalStatusOptions}}
                            <option value="{{Value}}"{{#if Selected}} selected{{/if}}>{{Text}}</option>
                            {{/each}}
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏字段 -->
    <input type="hidden" name="action" value="{{FormData.HiddenFields.Action}}">
    <input type="hidden" name="siteid" value="{{FormData.HiddenFields.SiteId}}">
    <input type="hidden" name="classid" value="{{FormData.HiddenFields.ClassId}}">
    <input type="hidden" name="backurl" value="{{FormData.HiddenFields.BackUrl}}">
    <input type="hidden" name="sex" value="{{FormData.PersonalInfo.Gender}}">

    <!-- 保存按钮 -->
    <div class="form-actions">
        <button type="submit" class="form-submit">
            <i data-lucide="save" class="icon"></i>
            保存修改
        </button>
    </div>
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证和提交处理
        const form = document.getElementById('profile-form');
        const submitButton = form.querySelector('.form-submit');
        
        // 表单提交处理
        form.addEventListener('submit', function(e) {
            // 显示提交状态
            submitButton.innerHTML = '<i data-lucide="loader-2" class="icon animate-spin"></i>保存中...';
            submitButton.disabled = true;
            
            // 重新创建图标
            setTimeout(() => {
                lucide.createIcons();
            }, 10);
        });

        // 输入验证
        const inputs = form.querySelectorAll('.form-input');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                // 清除错误状态
                if (this.classList.contains('error')) {
                    this.classList.remove('error');
                    const errorMsg = this.parentNode.querySelector('.form-error');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                }
            });
        });

        // 字段验证函数
        function validateField(field) {
            const value = field.value.trim();
            const name = field.name;
            let isValid = true;
            let errorMessage = '';

            // 昵称验证
            if (name === 'tonickname') {
                if (value.length === 0) {
                    isValid = false;
                    errorMessage = '昵称不能为空';
                } else if (value.length > 15) {
                    isValid = false;
                    errorMessage = '昵称不能超过15个字符';
                }
            }
            
            // 手机号验证
            else if (name === 'mobile' && value.length > 0) {
                if (!/^\d{11}$/.test(value)) {
                    isValid = false;
                    errorMessage = '手机号必须为11位数字';
                }
            }
            
            // 邮箱验证
            else if (name === 'email' && value.length > 0) {
                if (!/^.+@.+\..+$/.test(value)) {
                    isValid = false;
                    errorMessage = '邮箱格式不正确';
                }
            }
            
            // QQ号验证
            else if (name === 'qq' && value.length > 0) {
                if (!/^\d{5,11}$/.test(value)) {
                    isValid = false;
                    errorMessage = 'QQ号必须为5-11位数字';
                }
            }

            // 显示验证结果
            if (!isValid) {
                field.classList.add('error');
                showFieldError(field, errorMessage);
            } else {
                field.classList.remove('error');
                clearFieldError(field);
            }

            return isValid;
        }

        // 显示字段错误
        function showFieldError(field, message) {
            clearFieldError(field);
            const errorDiv = document.createElement('div');
            errorDiv.className = 'form-error';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        }

        // 清除字段错误
        function clearFieldError(field) {
            const existingError = field.parentNode.querySelector('.form-error');
            if (existingError) {
                existingError.remove();
            }
        }

        // 初始化图标
        lucide.createIcons();
        
        // 展开更多字段功能
        const expandBtn = document.getElementById('expand-more-btn');
        const moreFields = document.getElementById('more-fields');
        const expandBtnText = expandBtn ? expandBtn.querySelector('.expand-btn-text') : null;
        let isExpanded = false;
        
        if (expandBtn && moreFields && expandBtnText) {
            expandBtn.addEventListener('click', function() {
                isExpanded = !isExpanded;
                
                if (isExpanded) {
                    // 展开
                    moreFields.style.display = 'block';
                    expandBtn.classList.add('expanded');
                    expandBtnText.textContent = '收起更多'; // 只更新文本内容
                    // CSS类将处理图标的旋转动画
                    
                    // 动画辅助类，平滑展开
                    moreFields.classList.add('expanding');
                    setTimeout(() => {
                        moreFields.classList.remove('expanding');
                        const rect = moreFields.getBoundingClientRect();
                        const isVisible = rect.top >= 0 && 
                                          rect.left >= 0 && 
                                          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && 
                                          rect.right <= (window.innerWidth || document.documentElement.clientWidth);
                        
                        if (!isVisible) {
                            const headerHeight = 60;
                            const scrollPosition = moreFields.offsetTop - headerHeight;
                            window.scrollTo({
                                top: scrollPosition,
                                behavior: 'smooth'
                            });
                        }
                    }, 200); // 动画持续时间
                } else {
                    // 收起
                    expandBtn.classList.remove('expanded');
                    expandBtnText.textContent = '展开更多'; // 只更新文本内容
                    // CSS类将处理图标的旋转动画
                    
                    // 动画辅助类，平滑收起
                    moreFields.classList.add('collapsing');
                    setTimeout(() => {
                        moreFields.style.display = 'none';
                        moreFields.classList.remove('collapsing');
                    }, 200); // 动画持续时间
                }
            });
        }
    });
</script> 